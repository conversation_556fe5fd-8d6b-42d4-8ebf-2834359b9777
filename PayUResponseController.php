<?php
// PayU Response Handler - PHP Implementation
// Add this to your backend to handle /user/payment/response-payu endpoint

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PayUResponseController extends Controller
{
    /**
     * Handle PayU payment response
     */
    public function handleResponse(Request $request)
    {
        try {
            Log::info('PayU Response received', $request->all());
            
            $status = $request->input('status');
            $txnid = $request->input('txnid');
            $hash = $request->input('hash');
            $response = $request->input('response');
            
            // 1. VALIDATE REQUIRED PARAMETERS
            if (!$status || !$txnid) {
                Log::error('Missing required parameters', ['status' => $status, 'txnid' => $txnid]);
                return response('missing_parameters', 400);
            }
            
            // 2. DATABASE TRANSACTION LOOKUP
            $transaction = $this->findTransaction($txnid);
            
            if (!$transaction) {
                Log::error('Transaction not found', ['txnid' => $txnid]);
                // Return "cancelled" to match your client expectations
                return response('cancelled', 200);
            }
            
            Log::info('Transaction found', ['id' => $transaction->id]);
            
            // 3. STATUS UPDATE FROM PENDING TO CANCELLED/REJECTED
            $newStatus = $this->mapPayUStatus($status);
            
            $this->updateTransactionStatus($transaction, $newStatus, [
                'payu_response' => json_encode($response ?: []),
                'payu_hash' => $hash ?: '',
            ]);
            
            Log::info("Transaction {$txnid} updated: {$transaction->status} → {$newStatus}");
            
            // 4. RETURN STRING RESPONSE (as expected by client)
            return response($status, 200);
            
        } catch (\Exception $e) {
            Log::error('PayU processing error', ['error' => $e->getMessage()]);
            return response('error', 500);
        }
    }
    
    /**
     * Find transaction by PayU transaction ID
     */
    private function findTransaction($txnid)
    {
        // Search in payment_history table by multiple possible ID fields
        // Update table name and field names to match your actual database schema
        return DB::table('payment_history')
                 ->where('id', $txnid)
                 ->orWhere('transaction_id', $txnid)
                 ->orWhere('payu_txn_id', $txnid)
                 ->orWhere('reference_id', $txnid)
                 ->orderBy('created_at', 'desc')
                 ->first();
    }
    
    /**
     * Update transaction status in database
     */
    private function updateTransactionStatus($transaction, $newStatus, $additionalData = [])
    {
        try {
            // Update the transaction status in your database
            // Update this query to match your actual database schema
            DB::table('payment_history')
              ->where('id', $transaction->id)
              ->update([
                  'status' => $newStatus,
                  'remark' => $this->getRemarkForStatus($newStatus),
                  'updated_at' => now()
              ]);
            
            Log::info("Database updated: Transaction {$transaction->id} → {$newStatus}");
        } catch (\Exception $e) {
            Log::error("Database update error: {$e->getMessage()}");
            throw $e;
        }
    }
    
    /**
     * Map PayU status to your database status
     */
    private function mapPayUStatus($payuStatus)
    {
        $statusMapping = [
            'cancelled' => 'REJECTED',
            'failed' => 'REJECTED',
            'success' => 'COMPLETED',
            'pending' => 'PENDING'
        ];
        
        return $statusMapping[strtolower($payuStatus)] ?? 'REJECTED';
    }
    
    /**
     * Get remark for status
     */
    private function getRemarkForStatus($status)
    {
        $remarkMapping = [
            'REJECTED' => 'Payment Failed',
            'COMPLETED' => 'Payment Successful',
            'PENDING' => 'Payment Pending'
        ];
        
        return $remarkMapping[$status] ?? 'Payment Failed';
    }
}

// Add this route to your routes/api.php or routes/web.php:
// Route::post('/user/payment/response-payu', [PayUResponseController::class, 'handleResponse']);
?>
