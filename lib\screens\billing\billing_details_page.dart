import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import '../../models/billing_details_model.dart';
import '../../services/billing_details_service.dart';
import '../../services/global_connectivity_monitor.dart';
import '../../services/review_service.dart';
import '../station/station_details_page.dart';
import '../../utils/app_themes.dart';

/// Billing Details Page - Shows detailed invoice after charging session completion
/// ONLY USES REAL API DATA - NO MOCK DATA OR FALLBACKS
class BillingDetailsPage extends StatefulWidget {
  final String transactionId;
  final String? stationUid;
  final String? stationName;
  final String? sourceScreen; // Track source screen for conditional navigation

  const BillingDetailsPage({
    super.key,
    required this.transactionId,
    this.stationUid,
    this.stationName,
    this.sourceScreen, // 'charging_session' or 'charging_history'
  });

  @override
  State<BillingDetailsPage> createState() => _BillingDetailsPageState();
}

class _BillingDetailsPageState extends State<BillingDetailsPage>
    with TickerProviderStateMixin {
  BillingDetailsResponse? _billingDetails;
  bool _isLoading = true;
  bool _isDownloading = false;
  String? _errorMessage;
  String? _lastDownloadedTransactionId; // Track last downloaded invoice

  // GST section collapse/expand state
  bool _isGSTExpanded = false; // Default to collapsed state

  // Loading step animation controllers (kept for potential future use)
  late AnimationController _loadingStepsController;

  // Enhanced sequential loading state management
  String _currentLoadingPhase =
      'processing'; // 'processing', 'fetching', 'completed'
  String _currentLoadingMessage = 'Processing charging completion...';
  double _overallProgress = 0.0;

  final BillingDetailsService _billingService = BillingDetailsService();
  final ReviewService _reviewService = ReviewService();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();

    // Set context for global connectivity monitor
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        GlobalConnectivityMonitor().setContext(context);
      }
    });

    _fetchBillingDetails();
  }

  void _initializeAnimations() {
    // Initialize loading steps animation controller
    _loadingStepsController = AnimationController(
      duration: const Duration(milliseconds: 4500), // 4.5 seconds total
      vsync: this,
    );

    // Animation controllers removed - no longer using slide/fade animations
    // Currently using sequential phase-based loading instead of continuous animation
  }

  Future<void> _fetchBillingDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
        _currentLoadingPhase = 'processing';
        _currentLoadingMessage = 'Processing charging completion...';
        _overallProgress = 0.0;
      });

      debugPrint('🧾 ===== ENHANCED SEQUENTIAL BILLING FLOW STARTED =====');
      debugPrint('🧾 Transaction ID: "${widget.transactionId}"');
      debugPrint('🧾 Source Screen: ${widget.sourceScreen}');
      debugPrint(
          '🧾 REAL API MODE: Only real API data - no mock data or fallbacks');
      debugPrint('🧾 Station UID: ${widget.stationUid}');
      debugPrint('🧾 Station Name: ${widget.stationName}');

      // Validate transaction ID first
      if (widget.transactionId.isEmpty) {
        throw Exception('Transaction ID is empty');
      }

      // CONDITIONAL LOADING BEHAVIOR BASED ON SOURCE SCREEN
      if (widget.sourceScreen == 'charging_session') {
        // CHARGING SESSION FLOW: Apply 5-second processing delay
        debugPrint('🧾 ===== CHARGING SESSION COMPLETION FLOW =====');
        debugPrint('🧾 Source: charging_session_screen.dart');
        debugPrint('🧾 Applying 5-second processing delay for better UX');
        debugPrint(
            '🧾 This allows users time to mentally process charging completion');
        debugPrint('🧾 Sequential flow: Processing (5s) → API Fetch → Display');

        // PHASE 1: Enhanced 5-second processing phase with smooth progress updates
        for (int i = 0; i <= 50; i++) {
          if (!mounted) return;

          setState(() {
            _currentLoadingPhase = 'processing';
            _currentLoadingMessage = i < 15
                ? 'Processing charging completion...'
                : i < 30
                    ? 'Finalizing session data...'
                    : i < 45
                        ? 'Preparing billing information...'
                        : 'Generating invoice details...';
            _overallProgress = (i / 50) * 0.5; // 0% to 50% during processing
          });

          await Future.delayed(
              const Duration(milliseconds: 100)); // 5 seconds total
        }

        debugPrint(
            '🧾 ✅ 5-second processing phase completed - charging completion processed');

        if (!mounted) {
          debugPrint(
              '🧾 ⚠️ Widget unmounted during processing phase, aborting');
          return;
        }
      } else {
        // DIRECT ACCESS FLOW: Skip delay and proceed directly to API
        debugPrint('🧾 ===== DIRECT BILLING ACCESS FLOW =====');
        debugPrint(
            '🧾 Source: ${widget.sourceScreen ?? "unknown"} (charging_history, wallet, etc.)');
        debugPrint(
            '🧾 Skipping 3-second delay - proceeding directly to API fetch');
        debugPrint('🧾 Direct flow: API Fetch → Display');

        setState(() {
          _currentLoadingPhase = 'fetching';
          _currentLoadingMessage = 'Loading billing details...';
          _overallProgress =
              0.5; // Start at 50% since processing phase is skipped
        });
      }

      // PHASE 2: API FETCH
      debugPrint('🧾 ===== PHASE 2: FETCHING BILLING DATA FROM API =====');
      setState(() {
        _currentLoadingPhase = 'fetching';
        _currentLoadingMessage = 'Fetching billing details...';
        _overallProgress = 0.5;
      });

      debugPrint('🧾 Transaction ID: "${widget.transactionId}"');
      debugPrint('🧾 Transaction ID Length: ${widget.transactionId.length}');
      debugPrint('🧾 Transaction ID Type: ${widget.transactionId.runtimeType}');
      debugPrint(
          '🧾 API Endpoint: https://api2.eeil.online/api/v1/user/sessions/billing-details/${widget.transactionId}');
      debugPrint(
          '🧾 Using BillingDetailsService.getBillingDetailsWithValidation()');
      debugPrint('🧾 Authentication: Bearer token from TokenService');

      final startTime = DateTime.now();

      // Simulate API fetch progress
      setState(() {
        _currentLoadingMessage = 'Connecting to billing service...';
        _overallProgress = 0.6;
      });

      // ONLY REAL API CALL - NO MOCK DATA OR FALLBACKS
      final response = await _billingService
          .getBillingDetailsWithValidation(widget.transactionId);

      setState(() {
        _currentLoadingMessage = 'Processing billing data...';
        _overallProgress = 0.8;
      });

      debugPrint('🧾 ===== API RESPONSE RECEIVED =====');
      debugPrint('🧾 Response is null: ${response == null}');
      debugPrint('🧾 API Response Status: ${response?.success}');
      debugPrint('🧾 API Response Message: ${response?.statusMessage}');

      if (response?.success == true) {
        debugPrint('🧾 ✅ Real billing data fetched successfully');
        debugPrint('🧾 Invoice Number: ${response?.data.invoiceNumber}');
        debugPrint('🧾 Total Cost: ₹${response?.data.totalCost}');
        debugPrint('🧾 Station: ${response?.data.station.name}');
        debugPrint('🧾 Units: ${response?.data.units} kWh');
        debugPrint('🧾 Duration: ${response?.data.formattedDuration}');

        // Additional transaction analysis
        debugPrint('🧾 ===== TRANSACTION ANALYSIS =====');
        final totalCost = response?.data.totalCost ?? 0.0;
        final units = response?.data.units ?? 0.0;
        debugPrint('🧾 Has Charging Data: ${totalCost > 0}');
        debugPrint('🧾 Has Energy Transfer: ${units > 0}');
        debugPrint(
            '🧾 Transaction Type: ${totalCost > 0 ? "PAID_CHARGING" : "TEST_OR_FREE"}');
        debugPrint('🧾 Start Time: ${response?.data.startDateTime}');
        debugPrint('🧾 End Time: ${response?.data.endDateTime}');
        debugPrint('🧾 SOC Start: ${response?.data.socStart}%');
        debugPrint('🧾 SOC End: ${response?.data.socEnd}%');
        debugPrint('🧾 Base Cost: ₹${response?.data.cost}');
        debugPrint('🧾 CGST: ₹${response?.data.cgst}');
        debugPrint('🧾 SGST: ₹${response?.data.sgst}');
        debugPrint('🧾 IGST: ₹${response?.data.igst}');

        if (totalCost == 0) {
          debugPrint('🧾 ⚠️ ZERO COST TRANSACTION DETECTED');
          debugPrint('🧾 Possible reasons:');
          debugPrint('🧾 - Test/Demo transaction');
          debugPrint('🧾 - Promotional free charging');
          debugPrint('🧾 - Session cancelled before charging');
          debugPrint('🧾 - Incomplete session data');
        }
      } else {
        debugPrint('🧾 ❌ API call failed');
        debugPrint('🧾 Error Message: ${response?.statusMessage}');
        throw Exception(
            'API call failed: ${response?.statusMessage ?? "Unknown error"}');
      }

      // PHASE 3: FINALIZING
      setState(() {
        _currentLoadingMessage = 'Finalizing invoice...';
        _overallProgress = 0.9;
      });

      // Ensure minimum loading time of 1 second for better UX perception
      final elapsed = DateTime.now().difference(startTime);
      if (elapsed.inMilliseconds < 1000) {
        await Future.delayed(
            Duration(milliseconds: 1000 - elapsed.inMilliseconds));
      }

      setState(() {
        _currentLoadingPhase = 'completed';
        _currentLoadingMessage = 'Invoice ready!';
        _overallProgress = 1.0;
      });

      // Brief pause to show completion
      await Future.delayed(const Duration(milliseconds: 500));

      if (!mounted) return;

      if (response != null && response.success) {
        debugPrint(
            '🧾 ✅ Enhanced sequential billing flow completed successfully');
        setState(() {
          _billingDetails = response;
          _isLoading = false;
        });

        // Animations removed - content displays immediately
      } else {
        debugPrint('🧾 ❌ No billing details found or API returned error');
        setState(() {
          _errorMessage = response?.statusMessage ??
              'No billing details found for this transaction';
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('🧾 ❌ Error in enhanced sequential billing flow: $e');
      if (mounted) {
        setState(() {
          _errorMessage =
              'Failed to load billing details. Please check your connection and try again.';
          _isLoading = false;
        });
      }
    }
  }

  /// Handle conditional back navigation based on source screen
  void _handleBackNavigation() {
    debugPrint('🧾 ===== CONDITIONAL BACK NAVIGATION =====');
    debugPrint('🧾 Source Screen: ${widget.sourceScreen}');
    debugPrint('🧾 Transaction ID: ${widget.transactionId}');
    debugPrint('🧾 Station UID: ${widget.stationUid}');
    debugPrint('🧾 Station Name: ${widget.stationName}');

    switch (widget.sourceScreen) {
      case 'charging_session':
        // Navigate to home dashboard when coming from charging session
        debugPrint('🧾 ✅ Navigating to home dashboard (from charging session)');
        _navigateToHomeDashboard();
        break;

      case 'charging_history':
        // Navigate back to charging history page
        debugPrint(
            '🧾 ✅ Navigating back to charging history (normal back navigation)');
        _navigateBackToChargingHistory();
        break;

      default:
        // Fallback: Try station review navigation or normal back
        debugPrint('🧾 ⚠️ Unknown source screen, using fallback navigation');
        _navigateBackToStationReview();
        break;
    }
  }

  /// Navigate to home dashboard (for charging session completion)
  void _navigateToHomeDashboard() {
    debugPrint('🧾 Navigating to home dashboard');

    // Clear the entire navigation stack and go to dashboard
    Navigator.of(context)
        .pushNamedAndRemoveUntil(
      '/dashboard', // Navigate to main dashboard
      (route) => false, // Remove all previous routes
    )
        .then((_) {
      // Force refresh dashboard data after navigation to ensure fresh state
      debugPrint(
          '🧾 Triggering dashboard refresh after returning from billing');
      // The dashboard will automatically refresh when the route is navigated to
    });
  }

  /// Navigate back to charging history page (normal back navigation)
  void _navigateBackToChargingHistory() {
    debugPrint('🧾 Navigating back to charging history page');

    // Normal back navigation - maintains navigation stack
    Navigator.of(context).pop();
  }

  /// Navigate back to station details page review section (fallback)
  void _navigateBackToStationReview() {
    debugPrint('🧾 Fallback: Navigating back to station review section');
    debugPrint('🧾 Station UID: ${widget.stationUid}');
    debugPrint('🧾 Station Name: ${widget.stationName}');

    if (widget.stationUid != null) {
      // Navigate to station details page
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => StationDetailsPage(
            uid: widget.stationUid!,
            // Note: Will need to add logic to auto-scroll to review section
          ),
        ),
      );
    } else {
      // Fallback to normal back navigation if no station info
      debugPrint('🧾 No station UID provided, using normal back navigation');
      Navigator.of(context).pop();
    }
  }

  @override
  void dispose() {
    _loadingStepsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('🧾 build() called - transactionId: ${widget.transactionId}');
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _handleBackNavigation();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white, // Force light background
        appBar: AppBar(
          backgroundColor: const Color(0xFF4CAF50),
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: _handleBackNavigation,
          ),
          title: const Text(
            'Billing Details',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          centerTitle: false,
        ),
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    debugPrint(
        '🧾 _buildBody called - isLoading: $_isLoading, errorMessage: $_errorMessage, billingDetails: ${_billingDetails != null}');

    if (_isLoading) {
      debugPrint('🧾 Showing loading state');
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      debugPrint('🧾 Showing error state: $_errorMessage');
      return _buildErrorState();
    }

    if (_billingDetails == null) {
      debugPrint('🧾 Showing empty state - no billing details');
      return _buildEmptyState();
    }

    debugPrint('🧾 Showing main content - billing details available');
    // Remove animations - display content immediately
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildWriteReviewButton(),
          _buildChargingTimeline(),
          _buildMetricsGrid(),
          const SizedBox(height: 12),
          _buildPaymentDetails(),
          _buildGSTInformation(),
          const SizedBox(height: 16),
          _buildDownloadSection(), // Integrated download section
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF4CAF50),
            Color(0xFF45A049),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 40),
            // Loading text with animation
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 1500),
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Opacity(
                  opacity: value,
                  child: const Text(
                    'Generating Your Invoice',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 1.2,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              },
            ),
            const SizedBox(height: 8),
            // Source indicator (conditional)
            if (widget.sourceScreen == 'charging_session')
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.electric_bolt,
                      color: Colors.white.withValues(alpha: 0.9),
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Charging Session Complete',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              )
            else
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.history,
                      color: Colors.white.withValues(alpha: 0.9),
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Billing History Access',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 16),
            // Dynamic subtitle based on current loading phase
            Text(
              _currentLoadingMessage,
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.9),
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Animated Receipt/Invoice Icons
            _buildAnimatedInvoiceIcons(),

            const SizedBox(height: 32),
            // Sequential phase indicators (conditional based on source screen)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 40),
              child: Column(
                children: [
                  // Only show processing step for charging session flow
                  if (widget.sourceScreen == 'charging_session')
                    _buildSequentialPhaseStep(
                      'Processing completion',
                      _currentLoadingPhase == 'processing',
                      _currentLoadingPhase == 'fetching' ||
                          _currentLoadingPhase == 'completed',
                      Icons.check_circle,
                    ),
                  if (widget.sourceScreen == 'charging_session')
                    const SizedBox(height: 12),
                  _buildSequentialPhaseStep(
                    'Fetching billing data',
                    _currentLoadingPhase == 'fetching',
                    _currentLoadingPhase == 'completed',
                    Icons.cloud_download,
                  ),
                  const SizedBox(height: 12),
                  _buildSequentialPhaseStep(
                    'Finalizing invoice',
                    _currentLoadingPhase == 'completed',
                    false,
                    Icons.receipt_long,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 40),
            // Transaction ID display
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.confirmation_number,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Transaction: ${widget.transactionId}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedInvoiceIcons() {
    return SizedBox(
      height: 120,
      child: Center(
        child: Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.4),
              width: 2,
            ),
          ),
          child: const Icon(
            Icons.receipt_long,
            color: Colors.white,
            size: 40,
          ),
        ),
      ),
    );
  }

  Widget _buildSequentialPhaseStep(
      String text, bool isActive, bool isCompleted, IconData icon) {
    return Row(
      children: [
        // Phase indicator
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isCompleted
                ? Colors.green
                : isActive
                    ? Colors.white
                    : Colors.transparent,
            border: Border.all(
              color: isCompleted
                  ? Colors.green
                  : isActive
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.4),
              width: 2,
            ),
          ),
          child: isCompleted
              ? const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 14,
                )
              : isActive
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _currentLoadingPhase == 'completed'
                              ? Colors.green
                              : Color(0xFF4CAF50),
                        ),
                        backgroundColor: Colors.white.withValues(alpha: 0.3),
                      ),
                    )
                  : Icon(
                      icon,
                      color: Colors.white.withValues(alpha: 0.4),
                      size: 12,
                    ),
        ),
        const SizedBox(width: 16),

        // Phase text
        Expanded(
          child: AnimatedDefaultTextStyle(
            duration: const Duration(milliseconds: 300),
            style: TextStyle(
              color: isCompleted || isActive
                  ? Colors.white
                  : Colors.white.withValues(alpha: 0.5),
              fontSize: 14,
              fontWeight:
                  isCompleted || isActive ? FontWeight.w600 : FontWeight.normal,
            ),
            child: Text(text),
          ),
        ),

        // Status indicator
        if (isCompleted)
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 16,
          )
        else if (isActive)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              'Processing...',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.9),
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Connection Problem',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.red.shade400,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Unknown error occurred',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _fetchBillingDetails,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    debugPrint('🧾 Building empty state');
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.receipt_long, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            'No billing details available',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),
          Text(
            'Transaction ID: ${widget.transactionId}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: _fetchBillingDetails,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildChargingTimeline() {
    final billing = _billingDetails!.data;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 8), // Reduced margins
      padding: const EdgeInsets.all(16), // Reduced padding
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12), // Smaller radius
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 12,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline Header - Compact
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6), // Smaller padding
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.timeline,
                  color: Colors.green,
                  size: 16, // Smaller icon
                ),
              ),
              const SizedBox(width: 10),
              const Text(
                'Charging Timeline',
                style: TextStyle(
                  fontSize: 16, // Smaller font
                  fontWeight: FontWeight.w700,
                  color: Colors.black87, // Force light theme color
                ),
              ),
            ],
          ),
          const SizedBox(height: 16), // Reduced spacing

          // Professional Timeline Flow
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Timeline connector column
              Column(
                children: [
                  // Start dot
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 3),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.green.withValues(alpha: 0.3),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),

                  // Connecting line - Shorter
                  Container(
                    width: 3,
                    height: 40, // Reduced height
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.green,
                          Colors.green.withValues(alpha: 0.5),
                          Colors.orange.withValues(alpha: 0.5),
                          Colors.orange,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // Duration dot with timer icon - Smaller
                  Container(
                    width: 20, // Reduced size
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.orange.withValues(alpha: 0.3),
                          blurRadius: 6,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.timer,
                      color: Colors.white,
                      size: 12, // Smaller icon
                    ),
                  ),

                  // Connecting line - Shorter
                  Container(
                    width: 3,
                    height: 40, // Reduced height
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.orange,
                          Colors.orange.withValues(alpha: 0.5),
                          Colors.red.withValues(alpha: 0.5),
                          Colors.red,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // End dot
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 3),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withValues(alpha: 0.3),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.stop,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                ],
              ),

              const SizedBox(width: 20),

              // Timeline content
              Expanded(
                child: Column(
                  children: [
                    // Start section - Compact
                    SizedBox(
                      height: 40, // Reduced height
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.play_circle_filled,
                                color: Colors.green,
                                size: 16, // Smaller icon
                              ),
                              const SizedBox(width: 6),
                              const Text(
                                'Charging Started',
                                style: TextStyle(
                                  fontSize: 14, // Smaller font
                                  fontWeight: FontWeight.w600,
                                  color:
                                      Colors.black87, // Force light theme color
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 2),
                          Text(
                            billing.formattedStartTime,
                            style: TextStyle(
                              fontSize: 12, // Smaller font
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Duration section with enhanced timer icons - Compact
                    SizedBox(
                      height: 60, // Reduced height
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8), // Reduced padding
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.orange.withValues(alpha: 0.1),
                                  Colors.orange.withValues(alpha: 0.05),
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius:
                                  BorderRadius.circular(8), // Smaller radius
                              border: Border.all(
                                color: Colors.orange.withValues(alpha: 0.2),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                // Simplified timer icon
                                Icon(
                                  Icons.timer,
                                  color: Colors.orange,
                                  size: 18, // Smaller icon
                                ),
                                const SizedBox(width: 8),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Duration',
                                      style: TextStyle(
                                        fontSize: 12, // Smaller font
                                        fontWeight: FontWeight.w600,
                                        color: Colors
                                            .orange, // Keep orange for duration
                                      ),
                                    ),
                                    Text(
                                      billing.formattedDuration,
                                      style: const TextStyle(
                                        fontSize: 14, // Smaller font
                                        fontWeight: FontWeight.w700,
                                        color: Colors
                                            .black87, // Force light theme color
                                      ),
                                    ),
                                  ],
                                ),
                                const Spacer(),
                                // Additional timer icon
                                Icon(
                                  Icons.access_time,
                                  color: Colors.orange.withValues(alpha: 0.7),
                                  size: 16, // Smaller icon
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // End section - Compact
                    SizedBox(
                      height: 40, // Reduced height
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.stop_circle,
                                color: Colors.red,
                                size: 16, // Smaller icon
                              ),
                              const SizedBox(width: 6),
                              const Text(
                                'Charging Completed',
                                style: TextStyle(
                                  fontSize: 14, // Smaller font
                                  fontWeight: FontWeight.w600,
                                  color:
                                      Colors.black87, // Force light theme color
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 2),
                          Text(
                            billing.formattedEndTime,
                            style: TextStyle(
                              fontSize: 12, // Smaller font
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsGrid() {
    final billing = _billingDetails!.data;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Metrics Header - Compact
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6), // Reduced padding
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.analytics,
                  color: Colors.blue,
                  size: 16, // Smaller icon
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                'Charging Metrics',
                style: TextStyle(
                  fontSize: 16, // Smaller font
                  fontWeight: FontWeight.w700,
                  color: Colors.black87, // Force light theme color
                ),
              ),
            ],
          ),
          const SizedBox(height: 12), // Reduced spacing

          // New Layout: Left Column (Energy + SOC) | Right Column (Connector)
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left Column: Energy Consumed and SOC stacked vertically
              Expanded(
                child: Column(
                  children: [
                    // Energy Consumed Card
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.battery_charging_full,
                                color: Colors.green[600],
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Energy Consumed',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors
                                      .grey[600], // Force light theme color
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${billing.units} kWh',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87, // Force light theme color
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(
                        height:
                            8), // Reduced spacing between Energy and SOC cards

                    // SOC Card
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.battery_std,
                                color: Colors.green[600],
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'SoC',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors
                                      .grey[600], // Force light theme color
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${billing.socStart}% → ${billing.socEnd}%',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87, // Force light theme color
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12), // Gap between columns

              // Right Column: Connector with perfect alignment to SOC section
              Expanded(
                child: Container(
                  constraints: const BoxConstraints(
                    minHeight:
                        160, // Optimized height for perfect bottom alignment with left column
                  ),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment
                        .start, // Position content at top of card
                    children: [
                      const SizedBox(
                          height:
                              4), // Top padding for proper spacing from edge

                      // Header row with icon and label
                      Row(
                        children: [
                          Icon(
                            Icons.electrical_services,
                            color: Colors.green[600],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Connector',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Colors.grey[600], // Force light theme color
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Connector type text
                      Text(
                        '${billing.connector} (${billing.connectorType})',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87, // Force light theme color
                        ),
                      ),

                      const SizedBox(height: 12), // Space between text and icon

                      // Visual connector icon to fill empty space
                      Center(
                        child: Container(
                          width: 60, // Fixed width instead of constraints
                          height: 60, // Fixed height instead of constraints
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: AppThemes.primaryColor.withAlpha(25),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppThemes.primaryColor.withAlpha(77),
                              width: 1.5,
                            ),
                          ),
                          child: Icon(
                            Icons.electrical_services,
                            size: 28,
                            color: AppThemes.primaryColor,
                          ),
                        ),
                      ),

                      const SizedBox(
                          height:
                              16), // Increased bottom spacing for more height
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentDetails() {
    final billing = _billingDetails!.data;

    // Debug tax display logic
    debugPrint('🧾 ===== TAX DISPLAY DEBUG =====');
    debugPrint('🧾 IGST Show Flag: ${billing.igstShow}');
    debugPrint(
        '🧾 Tax Display Mode: ${billing.igstShow ? "IGST_ONLY" : "CGST_SGST"}');
    debugPrint('🧾 IGST Amount: ₹${billing.igst.toStringAsFixed(2)}');
    debugPrint('🧾 CGST Amount: ₹${billing.cgst.toStringAsFixed(2)}');
    debugPrint('🧾 SGST Amount: ₹${billing.sgst.toStringAsFixed(2)}');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Payment Details',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87, // Force light theme color
            ),
          ),
          const SizedBox(height: 16),
          _buildPaymentRow(
              'Charging Cost', '₹${billing.cost.toStringAsFixed(2)}'),
          // Conditional tax display based on igstShow flag
          if (billing.igstShow)
            _buildPaymentRow(
                'IGST (18%)', '₹${billing.igst.toStringAsFixed(2)}')
          else ...[
            _buildPaymentRow(
                'CGST (9%)', '₹${billing.cgst.toStringAsFixed(2)}'),
            _buildPaymentRow(
                'SGST (9%)', '₹${billing.sgst.toStringAsFixed(2)}'),
          ],
          const Divider(height: 24),
          _buildPaymentRow(
            'Total',
            '₹${billing.totalCost.toStringAsFixed(2)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentRow(String label, String amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
              color: isTotal
                  ? Colors.black87
                  : Colors.grey[600], // Force light theme colors
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
              color: isTotal
                  ? Colors.green
                  : Colors.black87, // Force light theme colors
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGSTInformation() {
    final billing = _billingDetails!.data;

    // Debug GST data to help identify the issue
    debugPrint('🧾 ===== GST SECTION DEBUG =====');
    debugPrint('🧾 GST Number: "${billing.gstNumber}"');
    debugPrint('🧾 GST Address: "${billing.gstAddress}"');
    debugPrint('🧾 IGST: ${billing.igst}');
    debugPrint('🧾 CGST: ${billing.cgst}');
    debugPrint('🧾 SGST: ${billing.sgst}');
    debugPrint('🧾 GST Section should be visible now');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Collapsible header with toggle button
          InkWell(
            onTap: () {
              setState(() {
                _isGSTExpanded = !_isGSTExpanded;
              });
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  // GST icon
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.receipt_long,
                      color: Colors.blue,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 10),
                  // GST Information title
                  const Expanded(
                    child: Text(
                      'GST Information',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87, // Force light theme color
                      ),
                    ),
                  ),
                  // Expand/collapse indicator
                  AnimatedRotation(
                    turns: _isGSTExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Collapsible content with smooth animation
          AnimatedCrossFade(
            firstChild: const SizedBox.shrink(), // Collapsed state
            secondChild: Column(
              children: [
                const SizedBox(height: 16),
                _buildGSTRow(
                    'GST Number:',
                    billing.gstNumber.isNotEmpty
                        ? billing.gstNumber
                        : 'Not Available'),
                const SizedBox(height: 8),
                _buildGSTRow(
                    'GST Address:',
                    billing.gstAddress.isNotEmpty
                        ? billing.gstAddress
                        : 'Not Available'),
              ],
            ), // Expanded state
            crossFadeState: _isGSTExpanded
                ? CrossFadeState.showSecond
                : CrossFadeState.showFirst,
            duration: const Duration(milliseconds: 300),
            sizeCurve: Curves.easeInOut,
            firstCurve: Curves.easeInOut,
            secondCurve: Curves.easeInOut,
          ),
        ],
      ),
    );
  }

  Widget _buildGSTRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600], // Force light theme color
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87, // Force light theme color
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWriteReviewButton() {
    return Container(
      margin:
          const EdgeInsets.fromLTRB(16, 8, 16, 8), // Reduced vertical margins
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _showWriteReviewDialog,
        icon: const Icon(Icons.rate_review,
            color: Colors.white, size: 18), // Smaller icon
        label: const Text(
          'Write Review',
          style: TextStyle(
            fontSize: 15, // Slightly smaller font
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF43A047),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12), // Reduced padding
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          elevation: 2,
        ),
      ),
    );
  }

  // Download section integrated into page content
  Widget _buildDownloadSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.file_download,
                  color: Colors.green,
                  size: 16,
                ),
              ),
              const SizedBox(width: 10),
              const Text(
                'Invoice Actions',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: Colors.black87, // Force light theme color
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Action buttons
          Row(
            children: [
              // Share button
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _shareInvoice,
                  icon: const Icon(Icons.share, size: 18),
                  label: const Text(
                    'Share',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    side: BorderSide(color: Colors.grey.shade300),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Download button with loading state
              Expanded(
                flex: 2,
                child: ElevatedButton.icon(
                  onPressed: _isDownloading ? null : _downloadInvoice,
                  icon: _isDownloading
                      ? const SizedBox(
                          width: 18,
                          height: 18,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.download,
                          color: Colors.white, size: 18),
                  label: Text(
                    _isDownloading ? 'Downloading...' : 'Download Invoice',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _isDownloading ? Colors.grey : const Color(0xFF4CAF50),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 2,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Enhanced share invoice functionality with PDF sharing
  Future<void> _shareInvoice() async {
    if (_billingDetails == null) return;

    final billing = _billingDetails!.data;
    final transactionId = widget.transactionId;

    // Show loading dialog while preparing share
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Preparing invoice for sharing...'),
          ],
        ),
      ),
    );

    try {
      // First, try to download the PDF
      debugPrint('📤 ===== ENHANCED PDF SHARING =====');
      debugPrint('📤 Attempting to download PDF for sharing...');

      final bytes = await _billingService.downloadInvoicePDF(transactionId);
      debugPrint('📤 PDF downloaded successfully: ${bytes.length} bytes');

      // Save PDF to temporary location for sharing
      final tempDir = await getTemporaryDirectory();
      final fileName = 'invoice_$transactionId.pdf';
      final tempFile = File('${tempDir.path}/$fileName');
      await tempFile.writeAsBytes(bytes);

      debugPrint('📤 PDF saved to temp location: ${tempFile.path}');

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Share the PDF file using native share functionality
      try {
        await Share.shareXFiles(
          [XFile(tempFile.path)],
          text: '''🔋 EV Charging Invoice

Invoice: ${billing.invoiceNumber}
Station: ${billing.station.name}
Energy: ${billing.units} kWh
Duration: ${billing.formattedDuration}
SOC: ${billing.socStart}% → ${billing.socEnd}%
Total: ₹${billing.totalCost.toStringAsFixed(2)}

Charged at: ${billing.fullStationAddress}
Date: ${billing.formattedEndTime}

Thank you for choosing eco-friendly transportation! 🌱''',
          subject: 'EV Charging Invoice - ${billing.invoiceNumber}',
        );

        debugPrint('✅ PDF shared successfully via native share');

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white, size: 20),
                  SizedBox(width: 8),
                  Text('Invoice shared successfully!'),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } catch (shareError) {
        debugPrint('❌ Error sharing PDF: $shareError');
        // Fallback to text sharing if PDF sharing fails
        await _shareTextFallback(billing);
      }
    } catch (downloadError) {
      debugPrint('❌ Error downloading PDF for sharing: $downloadError');

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Fallback to text sharing if PDF download fails
      await _shareTextFallback(billing);
    }
  }

  // Fallback text sharing method
  Future<void> _shareTextFallback(dynamic billing) async {
    debugPrint('📤 Using text sharing fallback...');

    final shareText = '''🔋 EV Charging Invoice

Invoice: ${billing.invoiceNumber}
Station: ${billing.station.name}
Energy: ${billing.units} kWh
Duration: ${billing.formattedDuration}
SOC: ${billing.socStart}% → ${billing.socEnd}%
Total: ₹${billing.totalCost.toStringAsFixed(2)}

Charged at: ${billing.fullStationAddress}
Date: ${billing.formattedEndTime}

Thank you for choosing eco-friendly transportation! 🌱''';

    try {
      await Share.share(
        shareText,
        subject: 'EV Charging Invoice - ${billing.invoiceNumber}',
      );

      debugPrint('✅ Text shared successfully as fallback');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.info, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Expanded(
                    child: Text(
                        'Invoice details shared as text (PDF unavailable)')),
              ],
            ),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Error in text sharing fallback: $e');

      // Final fallback - copy to clipboard
      await Clipboard.setData(ClipboardData(text: shareText));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.content_copy, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Expanded(child: Text('Invoice details copied to clipboard')),
              ],
            ),
            backgroundColor: Colors.blue,
            behavior: SnackBarBehavior.floating,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Download invoice functionality - Based on backend team's implementation
  Future<void> _downloadInvoice() async {
    if (_isDownloading) {
      debugPrint('⚠️ Download already in progress, ignoring request');
      return; // Prevent multiple downloads
    }

    final transactionId = widget.transactionId;

    // Check if this invoice was already downloaded recently
    if (_lastDownloadedTransactionId == transactionId) {
      final shouldRedownload = await _showRedownloadConfirmation();
      if (!shouldRedownload) {
        return;
      }
    }

    setState(() {
      _isDownloading = true;
    });

    try {
      // Get transaction ID from billing details
      final transactionId = widget.transactionId;
      if (transactionId.isEmpty) {
        throw Exception('Transaction ID is missing');
      }

      debugPrint('📄 ===== USING REAL API SERVICE FOR INVOICE DOWNLOAD =====');
      debugPrint('📄 Transaction ID: $transactionId');
      debugPrint(
          '📄 API Endpoint: https://api2.eeil.online/api/v1/user/reports/trans-invoice-download?id=$transactionId');
      debugPrint('📄 Using BillingDetailsService.downloadInvoicePDF()');
      debugPrint('📄 Same authentication as billing API calls (Bearer token)');
      debugPrint('📄 Expected Response: PDF binary data');

      // Use the proper API service method (same as billing API calls)
      final bytes = await _billingService.downloadInvoicePDF(transactionId);

      debugPrint('✅ PDF downloaded successfully via API service');
      debugPrint('📄 PDF size: ${bytes.length} bytes');

      // Save PDF to device storage
      await _savePDFToDevice(bytes, transactionId);

      // Mark this transaction as downloaded
      _lastDownloadedTransactionId = transactionId;
    } catch (e) {
      debugPrint('❌ Error downloading invoice: $e');

      // Provide specific error messages based on error type
      String errorMessage;
      if (e.toString().contains('SocketException') ||
          e.toString().contains('NetworkException')) {
        errorMessage =
            'Network error: Please check your internet connection and try again.';
      } else if (e.toString().contains('TimeoutException')) {
        errorMessage =
            'Download timeout: The server is taking too long to respond.';
      } else if (e.toString().contains('Authentication')) {
        errorMessage = 'Authentication failed: Please log in again.';
      } else if (e.toString().contains('Permission')) {
        errorMessage =
            'Permission denied: Please allow storage access in app settings.';
      } else if (e.toString().contains('Storage') ||
          e.toString().contains('space')) {
        errorMessage =
            'Insufficient storage space: Please free up some space and try again.';
      } else {
        errorMessage = 'Download failed: ${e.toString()}';
      }

      _showErrorMessage(errorMessage);
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  // Show redownload confirmation dialog
  Future<bool> _showRedownloadConfirmation() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Download Again?'),
            content: const Text(
                'This invoice has already been downloaded. Do you want to download it again?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('CANCEL'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4CAF50),
                ),
                child: const Text(
                  'DOWNLOAD AGAIN',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  // Save PDF to device storage
  Future<void> _savePDFToDevice(Uint8List bytes, String transactionId) async {
    try {
      // Request storage permission
      final permission = await _requestStoragePermission();
      if (!permission) {
        _showErrorMessage('Storage permission is required to save the invoice');
        return;
      }

      // Get the documents directory
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'invoice_$transactionId.pdf';
      final filePath = '${directory.path}/$fileName';

      debugPrint('Saving PDF to: $filePath');

      // Create the file and write bytes
      final file = File(filePath);
      await file.writeAsBytes(bytes);

      debugPrint('✅ PDF saved successfully: ${file.lengthSync()} bytes');

      // Verify file was created and is readable
      if (await file.exists()) {
        final fileSize = await file.length();
        debugPrint(
            '✅ File verification: exists=${await file.exists()}, size=$fileSize bytes');

        // Show success message with file location
        _showSuccessMessage(
            'Invoice downloaded successfully!\nSaved to: Documents/$fileName\nSize: ${_formatFileSize(fileSize)}');

        // Automatically open the PDF file
        await _openPDFFile(filePath);
      } else {
        throw Exception('File was not created successfully');
      }
    } catch (e) {
      debugPrint('❌ Error saving PDF: $e');
      _showErrorMessage('Failed to save invoice: $e');
    }
  }

  // Request storage permission with improved handling
  Future<bool> _requestStoragePermission() async {
    try {
      debugPrint('📄 Checking storage permissions...');

      if (Platform.isAndroid) {
        final androidInfo = await _getAndroidVersion();
        debugPrint('📄 Android API level: $androidInfo');

        if (androidInfo >= 33) {
          // Android 13+ (API 33+) - Use scoped storage, no permission needed for app directories
          debugPrint('📄 Android 13+ detected - using scoped storage');
          return true;
        } else if (androidInfo >= 30) {
          // Android 11-12 (API 30-32) - Try manage external storage first
          debugPrint(
              '📄 Android 11-12 detected - checking manage external storage');

          try {
            var status = await Permission.manageExternalStorage.status;
            debugPrint('📄 Manage external storage status: $status');

            if (status.isDenied) {
              debugPrint('📄 Requesting manage external storage permission...');
              status = await Permission.manageExternalStorage.request();
              debugPrint('📄 Permission result: $status');
            }

            if (status.isGranted) {
              return true;
            }
          } catch (e) {
            debugPrint('📄 Manage external storage not available: $e');
          }

          // Fallback to regular storage permission
          var storageStatus = await Permission.storage.status;
          debugPrint('📄 Storage permission status: $storageStatus');

          if (storageStatus.isDenied) {
            storageStatus = await Permission.storage.request();
            debugPrint('📄 Storage permission result: $storageStatus');
          }

          if (storageStatus.isPermanentlyDenied) {
            debugPrint('📄 Storage permission permanently denied');
            _showPermissionDeniedDialog();
            return false;
          }

          return storageStatus.isGranted;
        } else {
          // Android 10 and below - Use regular storage permission
          debugPrint(
              '📄 Android 10 and below - using regular storage permission');

          var status = await Permission.storage.status;
          debugPrint('📄 Storage permission status: $status');

          if (status.isDenied) {
            debugPrint('📄 Requesting storage permission...');
            status = await Permission.storage.request();
            debugPrint('📄 Permission result: $status');
          }

          if (status.isPermanentlyDenied) {
            debugPrint(
                '📄 Permission permanently denied - opening app settings');
            _showPermissionDeniedDialog();
            return false;
          }

          return status.isGranted;
        }
      } else if (Platform.isIOS) {
        // iOS doesn't need explicit storage permission for app documents
        debugPrint('📄 iOS detected - no permission needed');
        return true;
      }

      return true;
    } catch (e) {
      debugPrint('❌ Error requesting storage permission: $e');
      // For any permission errors, we'll still try to save to app directory
      return true;
    }
  }

  // Show permission denied dialog with option to open settings
  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Permission Required'),
        content:
            const Text('Storage permission is required to download invoices. '
                'Please enable storage access in app settings to continue.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
            ),
            child: const Text(
              'OPEN SETTINGS',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  // Get Android SDK version using device_info_plus
  Future<int> _getAndroidVersion() async {
    try {
      if (Platform.isAndroid) {
        final deviceInfo = DeviceInfoPlugin();
        final androidInfo = await deviceInfo.androidInfo;
        final sdkInt = androidInfo.version.sdkInt;
        debugPrint('📱 Android SDK version: $sdkInt');
        return sdkInt;
      }
      return 30; // Default for non-Android platforms
    } catch (e) {
      debugPrint('⚠️ Error getting Android version: $e');
      return 30; // Default to API 30 for compatibility
    }
  }

  // Open PDF file with default viewer
  Future<void> _openPDFFile(String filePath) async {
    try {
      debugPrint('Opening PDF file: $filePath');

      final result = await OpenFile.open(filePath);
      debugPrint('Open file result: ${result.type} - ${result.message}');

      if (result.type == ResultType.done) {
        debugPrint('✅ PDF opened successfully');
      } else if (result.type == ResultType.noAppToOpen) {
        _showErrorMessage(
            'No app available to open PDF files. Please install a PDF viewer.');
      } else if (result.type == ResultType.fileNotFound) {
        _showErrorMessage('PDF file not found. Please try downloading again.');
      } else if (result.type == ResultType.permissionDenied) {
        _showErrorMessage('Permission denied to open the file.');
      } else {
        _showErrorMessage('Unable to open PDF: ${result.message}');
      }
    } catch (e) {
      debugPrint('❌ Error opening PDF: $e');
      _showErrorMessage('Failed to open PDF: $e');
    }
  }

  // Format file size for display
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  // Show write review dialog
  void _showWriteReviewDialog() {
    int rating = 0;
    final commentController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          final isDarkMode = Theme.of(context).brightness == Brightness.dark;

          return AlertDialog(
            backgroundColor: isDarkMode ? Colors.grey.shade900 : Colors.white,
            title: Text(
              'Write a Review',
              style: TextStyle(
                color: isDarkMode ? Colors.white : Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'How was your charging experience?',
                  style: TextStyle(
                    color: isDarkMode
                        ? Colors.grey.shade300
                        : Colors.grey.shade600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Rating',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(5, (index) {
                    return IconButton(
                      icon: Icon(
                        index < rating ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                        size: 32,
                      ),
                      onPressed: () {
                        setState(() {
                          rating = index + 1;
                        });
                      },
                    );
                  }),
                ),
                const SizedBox(height: 16),
                Text(
                  'Comment',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: commentController,
                  maxLines: 3,
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Share your experience...',
                    hintStyle: TextStyle(
                      color: isDarkMode
                          ? Colors.grey.shade400
                          : Colors.grey.shade600,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFF43A047)),
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'CANCEL',
                  style: TextStyle(
                    color: isDarkMode
                        ? Colors.grey.shade400
                        : Colors.grey.shade600,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  if (rating > 0 && commentController.text.isNotEmpty) {
                    Navigator.pop(context);
                    _submitReview(rating, commentController.text);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please add a rating and comment'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF43A047),
                ),
                child: const Text(
                  'SUBMIT',
                  style: TextStyle(
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // Submit review using ReviewService
  Future<void> _submitReview(int rating, String comment) async {
    try {
      // Get station location ID from billing details
      final billing = _billingDetails?.data;
      if (billing?.station.uid == null) {
        _showErrorMessage(
            'Cannot submit review: Station information is missing');
        return;
      }

      final String locationId = billing!.station.uid;
      debugPrint('🌟 Submitting review for location ID: $locationId');

      // Generate tags based on rating
      final List<String> tags = _reviewService.generateTagsFromRating(rating);

      // Submit review using ReviewService
      final response = await _reviewService.submitReview(
        locationId: locationId,
        rating: rating,
        comment: comment,
        tags: tags,
      );

      // Handle response
      if (response.success) {
        debugPrint('✅ Review submitted successfully');
        _showSuccessMessage(
            'Review submitted successfully! Thank you for your feedback.');
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      debugPrint('❌ Error submitting review: $e');
      _showErrorMessage('Failed to submit review: $e');
    }
  }

  // Show success message with enhanced formatting
  void _showSuccessMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  message,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(
              seconds: 5), // Longer duration for success messages
          margin: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          action: SnackBarAction(
            label: 'OK',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }

  // Show error message with enhanced formatting
  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.error,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  message,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          duration:
              const Duration(seconds: 6), // Longer duration for error messages
          margin: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          action: SnackBarAction(
            label: 'DISMISS',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }
}
