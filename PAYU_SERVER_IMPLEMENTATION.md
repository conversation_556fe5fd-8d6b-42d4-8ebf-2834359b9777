# PayU Server-Side Implementation - Complete Fix

## 🎯 **Server-Side Implementation for `/user/payment/response-payu`**

Based on your codebase analysis and PayU documentation, here's the complete server-side implementation to fix the database update issue:

## 🔧 **Node.js/Express Implementation**

```javascript
const express = require('express');
const crypto = require('crypto');
const mysql = require('mysql2/promise');
const app = express();

// Middleware to parse POST data
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// PayU Response Handler Endpoint
app.post('/user/payment/response-payu', async (req, res) => {
    try {
        console.log('🔔 PayU Response received:', req.body);
        
        const { status, txnid, hash, response } = req.body;
        
        // 1. VALIDATE REQUIRED PARAMETERS
        if (!status || !txnid) {
            console.error('❌ Missing required parameters:', { status, txnid });
            return res.status(400).send('missing_parameters');
        }
        
        // 2. DATABASE TRANSACTION LOOKUP
        const transaction = await findTransaction(txnid);
        
        if (!transaction) {
            console.error('❌ Transaction not found:', txnid);
            return res.status(200).send('transaction_not_found');
        }
        
        console.log('✅ Transaction found:', transaction.id);
        
        // 3. STATUS UPDATE FROM PENDING TO CANCELLED/REJECTED
        const newStatus = mapPayUStatus(status);
        
        await updateTransactionStatus(transaction.id, newStatus, {
            payu_response: JSON.stringify(response || {}),
            payu_hash: hash || '',
            updated_at: new Date()
        });
        
        console.log(`✅ Transaction ${txnid} updated: ${transaction.status} → ${newStatus}`);
        
        // 4. RETURN STRING RESPONSE (as expected by client)
        res.status(200).send(status);
        
    } catch (error) {
        console.error('❌ PayU processing error:', error);
        res.status(500).send('error');
    }
});

// Helper Functions
async function findTransaction(txnid) {
    const connection = await mysql.createConnection({
        host: process.env.DB_HOST,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME
    });
    
    try {
        // Search by multiple possible transaction ID fields
        const [rows] = await connection.execute(`
            SELECT * FROM transactions 
            WHERE payu_txn_id = ? 
               OR transaction_id = ? 
               OR reference_id = ?
            LIMIT 1
        `, [txnid, txnid, txnid]);
        
        return rows[0] || null;
    } finally {
        await connection.end();
    }
}

async function updateTransactionStatus(transactionId, newStatus, additionalData = {}) {
    const connection = await mysql.createConnection({
        host: process.env.DB_HOST,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME
    });
    
    try {
        await connection.execute(`
            UPDATE transactions 
            SET status = ?, 
                payu_response = ?,
                payu_hash = ?,
                updated_at = ?
            WHERE id = ?
        `, [
            newStatus,
            additionalData.payu_response || null,
            additionalData.payu_hash || null,
            additionalData.updated_at || new Date(),
            transactionId
        ]);
        
        console.log(`✅ Database updated: Transaction ${transactionId} → ${newStatus}`);
    } finally {
        await connection.end();
    }
}

function mapPayUStatus(payuStatus) {
    const statusMapping = {
        'cancelled': 'REJECTED',
        'failed': 'REJECTED',
        'success': 'COMPLETED',
        'pending': 'PENDING'
    };
    
    return statusMapping[payuStatus.toLowerCase()] || 'REJECTED';
}

module.exports = app;
```

## 🔧 **PHP/Laravel Implementation**

```php
<?php
// routes/api.php or routes/web.php
Route::post('/user/payment/response-payu', [PayUController::class, 'handleResponse']);

// app/Http/Controllers/PayUController.php
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Transaction;

class PayUController extends Controller
{
    public function handleResponse(Request $request)
    {
        try {
            Log::info('PayU Response received', $request->all());
            
            $status = $request->input('status');
            $txnid = $request->input('txnid');
            $hash = $request->input('hash');
            $response = $request->input('response');
            
            // 1. VALIDATE REQUIRED PARAMETERS
            if (!$status || !$txnid) {
                Log::error('Missing required parameters', ['status' => $status, 'txnid' => $txnid]);
                return response('missing_parameters', 400);
            }
            
            // 2. DATABASE TRANSACTION LOOKUP
            $transaction = $this->findTransaction($txnid);
            
            if (!$transaction) {
                Log::error('Transaction not found', ['txnid' => $txnid]);
                return response('transaction_not_found', 200);
            }
            
            Log::info('Transaction found', ['id' => $transaction->id]);
            
            // 3. STATUS UPDATE FROM PENDING TO CANCELLED/REJECTED
            $newStatus = $this->mapPayUStatus($status);
            
            $this->updateTransactionStatus($transaction, $newStatus, [
                'payu_response' => json_encode($response ?: []),
                'payu_hash' => $hash ?: '',
                'updated_at' => now()
            ]);
            
            Log::info("Transaction {$txnid} updated: {$transaction->status} → {$newStatus}");
            
            // 4. RETURN STRING RESPONSE (as expected by client)
            return response($status, 200);
            
        } catch (\Exception $e) {
            Log::error('PayU processing error', ['error' => $e->getMessage()]);
            return response('error', 500);
        }
    }
    
    private function findTransaction($txnid)
    {
        // Search by multiple possible transaction ID fields
        return Transaction::where('payu_txn_id', $txnid)
                         ->orWhere('transaction_id', $txnid)
                         ->orWhere('reference_id', $txnid)
                         ->first();
    }
    
    private function updateTransactionStatus($transaction, $newStatus, $additionalData = [])
    {
        $transaction->update([
            'status' => $newStatus,
            'payu_response' => $additionalData['payu_response'] ?? null,
            'payu_hash' => $additionalData['payu_hash'] ?? null,
            'updated_at' => $additionalData['updated_at'] ?? now()
        ]);
        
        Log::info("Database updated: Transaction {$transaction->id} → {$newStatus}");
    }
    
    private function mapPayUStatus($payuStatus)
    {
        $statusMapping = [
            'cancelled' => 'REJECTED',
            'failed' => 'REJECTED',
            'success' => 'COMPLETED',
            'pending' => 'PENDING'
        ];
        
        return $statusMapping[strtolower($payuStatus)] ?? 'REJECTED';
    }
}
```

## 🔧 **Python/Django Implementation**

```python
# views.py
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
import json
import logging
from .models import Transaction

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class PayUResponseView(View):
    
    @require_http_methods(["POST"])
    def post(self, request):
        try:
            logger.info(f'PayU Response received: {request.POST.dict()}')
            
            status = request.POST.get('status')
            txnid = request.POST.get('txnid')
            hash_value = request.POST.get('hash')
            response_data = request.POST.get('response')
            
            # 1. VALIDATE REQUIRED PARAMETERS
            if not status or not txnid:
                logger.error(f'Missing required parameters: status={status}, txnid={txnid}')
                return HttpResponse('missing_parameters', status=400)
            
            # 2. DATABASE TRANSACTION LOOKUP
            transaction = self.find_transaction(txnid)
            
            if not transaction:
                logger.error(f'Transaction not found: {txnid}')
                return HttpResponse('transaction_not_found', status=200)
            
            logger.info(f'Transaction found: {transaction.id}')
            
            # 3. STATUS UPDATE FROM PENDING TO CANCELLED/REJECTED
            new_status = self.map_payu_status(status)
            
            self.update_transaction_status(transaction, new_status, {
                'payu_response': json.dumps(response_data or {}),
                'payu_hash': hash_value or '',
            })
            
            logger.info(f'Transaction {txnid} updated: {transaction.status} → {new_status}')
            
            # 4. RETURN STRING RESPONSE (as expected by client)
            return HttpResponse(status, status=200)
            
        except Exception as e:
            logger.error(f'PayU processing error: {str(e)}')
            return HttpResponse('error', status=500)
    
    def find_transaction(self, txnid):
        # Search by multiple possible transaction ID fields
        try:
            return Transaction.objects.filter(
                models.Q(payu_txn_id=txnid) |
                models.Q(transaction_id=txnid) |
                models.Q(reference_id=txnid)
            ).first()
        except Transaction.DoesNotExist:
            return None
    
    def update_transaction_status(self, transaction, new_status, additional_data=None):
        transaction.status = new_status
        if additional_data:
            transaction.payu_response = additional_data.get('payu_response')
            transaction.payu_hash = additional_data.get('payu_hash')
        transaction.save()
        
        logger.info(f'Database updated: Transaction {transaction.id} → {new_status}')
    
    def map_payu_status(self, payu_status):
        status_mapping = {
            'cancelled': 'REJECTED',
            'failed': 'REJECTED',
            'success': 'COMPLETED',
            'pending': 'PENDING'
        }
        
        return status_mapping.get(payu_status.lower(), 'REJECTED')

# urls.py
from django.urls import path
from .views import PayUResponseView

urlpatterns = [
    path('user/payment/response-payu', PayUResponseView.as_view(), name='payu_response'),
]
```

## 🗄️ **Database Schema Requirements**

Make sure your transactions table has these fields:

```sql
-- Add these columns if they don't exist
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS payu_txn_id VARCHAR(255);
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS payu_response TEXT;
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS payu_hash VARCHAR(255);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_payu_txn_id ON transactions(payu_txn_id);
CREATE INDEX IF NOT EXISTS idx_transaction_id ON transactions(transaction_id);
CREATE INDEX IF NOT EXISTS idx_reference_id ON transactions(reference_id);
```

## 🎯 **Key Implementation Points**

1. **✅ Database Lookup**: Searches by multiple transaction ID fields
2. **✅ Status Mapping**: Maps PayU status to your database status
3. **✅ String Response**: Returns string as expected by client
4. **✅ Error Handling**: Proper error responses and logging
5. **✅ Transaction Update**: Updates status from PENDING to REJECTED/COMPLETED

## 🚀 **Expected Results After Implementation**

**Before:**
```
Transaction ID: 20250707141343118754
Status: PENDING
```

**After:**
```
Transaction ID: 20250707141343118754  
Status: REJECTED (for cancelled payments)
```

**Client Logs Will Show:**
```
✅ PAYU API: Successfully processed PayU payment response (String)
✅ PAYU: Backend confirmed payment processing success
✅ PAYU: Transaction status successfully updated in database
```

Choose the implementation that matches your server technology and deploy it. This will fix the database update issue while keeping the string response format that the client now handles perfectly! 🚀
