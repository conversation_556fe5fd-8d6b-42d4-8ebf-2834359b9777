// PayU Response Handler - Server-Side Implementation
// Add this to your backend server to handle /user/payment/response-payu endpoint

const express = require('express');
const mysql = require('mysql2/promise');
const router = express.Router();

// Database configuration - update with your actual database credentials
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'your_db_user',
    password: process.env.DB_PASSWORD || 'your_db_password',
    database: process.env.DB_NAME || 'your_database_name'
};

// PayU Response Handler Endpoint
router.post('/user/payment/response-payu', async (req, res) => {
    try {
        console.log('🔔 PayU Response received:', req.body);
        
        const { status, txnid, hash, response } = req.body;
        
        // 1. VALIDATE REQUIRED PARAMETERS
        if (!status || !txnid) {
            console.error('❌ Missing required parameters:', { status, txnid });
            return res.status(400).send('missing_parameters');
        }
        
        // 2. DATABASE TRANSACTION LOOKUP
        const transaction = await findTransaction(txnid);
        
        if (!transaction) {
            console.error('❌ Transaction not found:', txnid);
            // Return "cancelled" to match your client expectations
            return res.status(200).send('cancelled');
        }
        
        console.log('✅ Transaction found:', transaction.id);
        
        // 3. STATUS UPDATE FROM PENDING TO CANCELLED/REJECTED
        const newStatus = mapPayUStatus(status);
        
        await updateTransactionStatus(transaction.id, newStatus, {
            payu_response: JSON.stringify(response || {}),
            payu_hash: hash || '',
            updated_at: new Date()
        });
        
        console.log(`✅ Transaction ${txnid} updated: ${transaction.status} → ${newStatus}`);
        
        // 4. RETURN STRING RESPONSE (as expected by client)
        res.status(200).send(status);
        
    } catch (error) {
        console.error('❌ PayU processing error:', error);
        res.status(500).send('error');
    }
});

// Helper function to find transaction by PayU transaction ID
async function findTransaction(txnid) {
    const connection = await mysql.createConnection(dbConfig);
    
    try {
        // Search by multiple possible transaction ID fields
        // Update these field names to match your actual database schema
        const [rows] = await connection.execute(`
            SELECT * FROM payment_history 
            WHERE id = ? 
               OR transaction_id = ? 
               OR payu_txn_id = ?
               OR reference_id = ?
            ORDER BY created_at DESC
            LIMIT 1
        `, [txnid, txnid, txnid, txnid]);
        
        return rows[0] || null;
    } catch (error) {
        console.error('❌ Database query error:', error);
        return null;
    } finally {
        await connection.end();
    }
}

// Helper function to update transaction status
async function updateTransactionStatus(transactionId, newStatus, additionalData = {}) {
    const connection = await mysql.createConnection(dbConfig);
    
    try {
        // Update the transaction status in your database
        // Update this query to match your actual database schema
        await connection.execute(`
            UPDATE payment_history 
            SET status = ?, 
                remark = ?,
                updated_at = NOW()
            WHERE id = ?
        `, [
            newStatus,
            getRemarkForStatus(newStatus),
            transactionId
        ]);
        
        console.log(`✅ Database updated: Transaction ${transactionId} → ${newStatus}`);
    } catch (error) {
        console.error('❌ Database update error:', error);
        throw error;
    } finally {
        await connection.end();
    }
}

// Helper function to map PayU status to your database status
function mapPayUStatus(payuStatus) {
    const statusMapping = {
        'cancelled': 'REJECTED',
        'failed': 'REJECTED',
        'success': 'COMPLETED',
        'pending': 'PENDING'
    };
    
    return statusMapping[payuStatus.toLowerCase()] || 'REJECTED';
}

// Helper function to get remark for status
function getRemarkForStatus(status) {
    const remarkMapping = {
        'REJECTED': 'Payment Failed',
        'COMPLETED': 'Payment Successful',
        'PENDING': 'Payment Pending'
    };
    
    return remarkMapping[status] || 'Payment Failed';
}

module.exports = router;

// Usage in your main server file (app.js or server.js):
// const payuHandler = require('./server_payu_response_handler');
// app.use('/api/v1', payuHandler);
