import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/app_themes.dart';

/// A professional bottom sheet for adding balance to the wallet
/// Features modern UI, smooth validation, and excellent user experience
class AddBalanceSheet extends StatefulWidget {
  /// Callback function when user confirms adding balance
  final Function(double amount, {String source}) onAddBalance;

  const AddBalanceSheet({
    super.key,
    required this.onAddBalance,
  });

  @override
  State<AddBalanceSheet> createState() => _AddBalanceSheetState();
}

class _AddBalanceSheetState extends State<AddBalanceSheet>
    with SingleTickerProviderStateMixin {
  // Text controller for the amount input
  final TextEditingController _amountController = TextEditingController();

  // Animation controller for smooth animations
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Minimum amount configuration - REMOVED RESTRICTION
  static const double minimumAmount = 1.0; // Changed from 50.0 to 1.0
  static const double maximumAmount = 50000.0;

  // Quick selection amounts (starting with minimum)
  final List<int> _quickAmounts = [
    50,
    100,
    500,
    1000
  ]; // Removed 1 and 10 rupees options

  // Currently selected quick amount (null if custom amount)
  int? _selectedAmount;

  // Validation state
  bool _isAmountValid = false;
  String? _errorMessage;

  // Payment gateway (default to PhonePe)
  String _selectedGateway = 'phonepe';

  // Focus node for the text field
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Initialize animations
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOutCubic),
    ));

    // Start the animation
    _animationController.forward();

    // Add listener for amount validation
    _amountController.addListener(_validateAmount);

    // Focus listener for UI updates
    _focusNode.addListener(() {
      setState(() {
        // Trigger rebuild for focus-dependent styling
      });
    });
  }

  @override
  void dispose() {
    _amountController.removeListener(_validateAmount);
    _amountController.dispose();
    _animationController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  // Smooth, professional validation without jarring feedback
  void _validateAmount() {
    final text = _amountController.text.trim();

    if (text.isEmpty) {
      setState(() {
        _selectedAmount = null;
        _isAmountValid = false;
        _errorMessage = null;
      });
      return;
    }

    final amount = double.tryParse(text);

    if (amount == null) {
      setState(() {
        _selectedAmount = null;
        _isAmountValid = false;
        _errorMessage = 'Please enter a valid number';
      });
      return;
    }

    if (amount < minimumAmount) {
      setState(() {
        _selectedAmount = null;
        _isAmountValid = false;
        _errorMessage = null; // Don't show error immediately for better UX
      });
      return;
    }

    if (amount > maximumAmount) {
      setState(() {
        _selectedAmount = null;
        _isAmountValid = false;
        _errorMessage = 'Maximum amount is ₹${maximumAmount.toInt()}';
      });
      return;
    }

    // Amount is valid
    setState(() {
      _isAmountValid = true;
      _errorMessage = null;

      // Check if matches quick amount
      final amountInt = amount.toInt();
      if (amount == amountInt && _quickAmounts.contains(amountInt)) {
        _selectedAmount = amountInt;
      } else {
        _selectedAmount = null;
      }
    });
  }

  // Handle quick amount selection with smooth animation
  void _selectQuickAmount(int amount) {
    setState(() {
      _selectedAmount = amount;
      _amountController.text = amount.toString();
      _isAmountValid = true;
      _errorMessage = null;
    });

    // Smooth unfocus
    _focusNode.unfocus();
  }

  // Professional validation and submission with inline feedback
  void _handleAddBalance() {
    final text = _amountController.text.trim();

    if (text.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter an amount';
      });
      return;
    }

    final amount = double.tryParse(text);

    if (amount == null) {
      setState(() {
        _errorMessage = 'Please enter a valid amount';
      });
      return;
    }

    if (amount < minimumAmount) {
      setState(() {
        _errorMessage = 'Minimum amount is ₹${minimumAmount.toInt()}';
      });
      return;
    }

    if (amount > maximumAmount) {
      setState(() {
        _errorMessage = 'Maximum amount is ₹${maximumAmount.toInt()}';
      });
      return;
    }

    // Clear any error messages
    setState(() {
      _errorMessage = null;
    });

    // Success - proceed with payment
    widget.onAddBalance(amount, source: _selectedGateway);
  }

  // Get professional input border color with enhanced visibility
  Color _getInputBorderColor(bool isDarkMode) {
    if (_errorMessage != null) {
      return Colors.red.shade400;
    }
    if (_focusNode.hasFocus) {
      return const Color(0xFF4776E6); // Use blue color when focused
    }
    // Default border colors for non-focused state
    return isDarkMode ? Colors.grey.shade500 : Colors.grey.shade300;
  }

  // Get input background color with enhanced contrast
  Color _getInputBackgroundColor(bool isDarkMode) {
    if (_focusNode.hasFocus) {
      return const Color(0xFF4776E6).withValues(alpha: 0.05); // Use blue background when focused
    }
    // Default background colors for non-focused state
    return isDarkMode ? AppThemes.darkCard : Colors.grey.shade50;
  }

  // Build custom input box for amount entry with solid blue border
  Widget _buildCustomInputBox(bool isDarkMode) {
    return Container(
      height: 58, // Fixed height
      decoration: BoxDecoration(
        color: _getInputBackgroundColor(isDarkMode),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _focusNode.hasFocus 
              ? const Color(0xFF4776E6) // Solid blue when focused
              : (isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300), // Gray when not focused
          width: 2.0, // Consistent border width
        ),
      ),
      child: Row(
        children: [
          // Rupee symbol container
          Container(
            width: 60,
            alignment: Alignment.center,
            child: Text(
              '₹',
              style: TextStyle(
                color: _focusNode.hasFocus 
                    ? const Color(0xFF4776E6) // Blue when focused
                    : (isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600), // Gray when not focused
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Text input field
          Expanded(
            child: TextField(
              controller: _amountController,
              focusNode: _focusNode,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
              style: TextStyle(
                color: isDarkMode ? AppThemes.darkTextPrimary : const Color(0xFF1A1A1A),
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              decoration: InputDecoration(
                hintText: 'Enter amount (₹${minimumAmount.toInt()} or more)',
                hintStyle: TextStyle(
                  color: isDarkMode ? Colors.grey.shade500 : Colors.grey.shade500,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none, // Remove all borders
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                focusedErrorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 0,
                  vertical: 16,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16), // Right padding
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor =
        isDarkMode ? AppThemes.primaryColor : const Color(0xFF4776E6);
    final secondaryColor = const Color(0xFF8E54E9);
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final screenHeight = MediaQuery.of(context).size.height;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: screenHeight * 0.9,
          ),
          decoration: BoxDecoration(
            color: isDarkMode ? AppThemes.darkSurface : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(28)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: isDarkMode ? 0.3 : 0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Scrollable content area
                Flexible(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.only(
                      left: 24,
                      right: 24,
                      top: 16,
                      bottom: keyboardHeight > 0 ? 16 : 0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Modern sheet handle
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: Center(
                            child: Container(
                              width: 48,
                              height: 4,
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? Colors.grey.shade600
                                    : Colors.grey.shade400,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 28),

                        // Professional title
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              children: [
                                Text(
                                  'Add Balance',
                                  style: TextStyle(
                                    fontSize: 28,
                                    fontWeight: FontWeight.w700,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF1A1A1A),
                                    letterSpacing: -0.5,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Add money to your wallet securely',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: isDarkMode
                                        ? AppThemes.darkTextSecondary
                                        : Colors.grey.shade600,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 32),

                        // Professional amount input
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Enter Amount',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF333333),
                                  ),
                                ),
                                const SizedBox(height: 12),
                                // Custom input box with solid blue border when focused
                                _buildCustomInputBox(isDarkMode),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 28),

                        // Modern quick amount selection
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Quick Select',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF333333),
                                  ),
                                ),
                                const SizedBox(height: 12),
                                Wrap(
                                  spacing: 12,
                                  runSpacing: 12,
                                  children: _quickAmounts.map((amount) {
                                    final isSelected =
                                        _selectedAmount == amount;
                                    return _buildModernQuickAmountButton(
                                      amount: amount,
                                      isSelected: isSelected,
                                      onTap: () => _selectQuickAmount(amount),
                                      primaryColor: primaryColor,
                                      secondaryColor: secondaryColor,
                                      isDarkMode: isDarkMode,
                                    );
                                  }).toList(),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 32),

                        // Professional payment method section
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Payment Method',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF333333),
                                  ),
                                ),
                                const SizedBox(height: 12),
                                _buildPaymentMethodOptions(
                                    isDarkMode, primaryColor),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),

                // Fixed bottom section with validation and button
                Container(
                  padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
                  decoration: BoxDecoration(
                    color: isDarkMode ? AppThemes.darkSurface : Colors.white,
                    border: Border(
                      top: BorderSide(
                        color: isDarkMode
                            ? AppThemes.darkBorder
                            : Colors.grey.shade200,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Validation message area (always present but conditionally visible)
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        height: _errorMessage != null ? 40 : 0,
                        child: _errorMessage != null
                            ? Container(
                                width: double.infinity,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? Colors.red.shade900
                                          .withValues(alpha: 0.2)
                                      : Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.red.shade400,
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.warning_amber_rounded,
                                      color: Colors.red.shade600,
                                      size: 18,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        _errorMessage!,
                                        style: TextStyle(
                                          color: isDarkMode
                                              ? Colors.red.shade300
                                              : Colors.red.shade700,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : const SizedBox.shrink(),
                      ),

                      const SizedBox(height: 16),

                      // Always visible add balance button
                      SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: _buildModernAddBalanceButton(
                              primaryColor, secondaryColor, isDarkMode),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Modern quick amount button with professional styling
  Widget _buildModernQuickAmountButton({
    required int amount,
    required bool isSelected,
    required VoidCallback onTap,
    required Color primaryColor,
    required Color secondaryColor,
    required bool isDarkMode,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
        padding: const EdgeInsets.symmetric(
            horizontal: 18,
            vertical: 10), // Reduced padding for more compact design
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [primaryColor, secondaryColor],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected
              ? null
              : (isDarkMode ? AppThemes.darkCard : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Colors.transparent
                : (isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300),
            width: 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Text(
          '₹$amount',
          style: TextStyle(
            color: isSelected
                ? Colors.white
                : (isDarkMode
                    ? AppThemes.darkTextPrimary
                    : const Color(0xFF333333)),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  // Professional payment method options with selection
  Widget _buildPaymentMethodOptions(bool isDarkMode, Color primaryColor) {
    return Column(
      children: [
        // PhonePe Option
        GestureDetector(
          onTap: () {
            setState(() {
              _selectedGateway = 'phonepe';
            });
          },
          child: _buildPaymentOption(
            isDarkMode: isDarkMode,
            primaryColor: primaryColor,
            isSelected: _selectedGateway == 'phonepe',
            icon: Icons.phone_android,
            iconColor: const Color(0xFF5F259F),
            title: 'PhonePe',
            subtitle: 'Fast & secure payments',
          ),
        ),
        const SizedBox(height: 12),
        // PayU Option
        GestureDetector(
          onTap: () {
            setState(() {
              _selectedGateway = 'payu';
            });
          },
          child: _buildPaymentOption(
            isDarkMode: isDarkMode,
            primaryColor: primaryColor,
            isSelected: _selectedGateway == 'payu',
            icon: Icons.payment,
            iconColor: const Color(0xFF00A651),
            title: 'PayU',
            subtitle: 'Secure online payments',
          ),
        ),
        const SizedBox(height: 12),
        // Cashfree Option - COMMENTED OUT
        /*
        GestureDetector(
          onTap: () {
            setState(() {
              _selectedGateway = 'cashfree';
            });
          },
          child: _buildPaymentOption(
            isDarkMode: isDarkMode,
            primaryColor: primaryColor,
            isSelected: _selectedGateway == 'cashfree',
            icon: Icons.account_balance_wallet,
            iconColor: const Color(0xFF0066CC),
            title: 'Cashfree',
            subtitle: 'Fast & reliable payments',
          ),
        ),
        */
      ],
    );
  }

  // Individual payment option widget
  Widget _buildPaymentOption({
    required bool isDarkMode,
    required Color primaryColor,
    required bool isSelected,
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOutCubic,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? AppThemes.darkCard : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected
              ? primaryColor
              : (isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: primaryColor.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: iconColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: isDarkMode
                        ? AppThemes.darkTextPrimary
                        : const Color(0xFF1A1A1A),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: isDarkMode
                        ? AppThemes.darkTextSecondary
                        : Colors.grey.shade600,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: isSelected ? primaryColor : Colors.transparent,
              shape: BoxShape.circle,
              border: isSelected
                  ? null
                  : Border.all(
                      color: isDarkMode
                          ? AppThemes.darkBorder
                          : Colors.grey.shade400,
                      width: 2,
                    ),
            ),
            child: isSelected
                ? const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  )
                : null,
          ),
        ],
      ),
    );
  }

  // Modern add balance button - always visible with professional styling
  Widget _buildModernAddBalanceButton(
      Color primaryColor, Color secondaryColor, bool isDarkMode) {
    // Button is always enabled but shows different states
    final canProceed = _amountController.text.isNotEmpty;

    return Container(
      height: 52, // Reduced from 56 to 52 for more compact design
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [primaryColor, secondaryColor],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _handleAddBalance(),
          borderRadius: BorderRadius.circular(16),
          splashColor: Colors.white.withValues(alpha: 0.2),
          highlightColor: Colors.white.withValues(alpha: 0.1),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (!canProceed) ...[
                  Icon(
                    Icons.info_outline,
                    color: Colors.white.withValues(alpha: 0.8),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  canProceed ? 'Add Balance' : 'Enter Amount to Continue',
                  style: TextStyle(
                    color: canProceed
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.9),
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    letterSpacing: -0.2,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
